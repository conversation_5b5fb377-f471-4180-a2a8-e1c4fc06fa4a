<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D建筑多点爆破拆除演示</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background: #000;
        }
        
        #canvas-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            min-width: 250px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        #controls h3 {
            margin-top: 0;
            color: #ff6b6b;
            text-align: center;
            font-size: 18px;
        }
        
        .control-group {
            margin: 15px 0;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #ddd;
        }
        
        select, button {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        select {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        button {
            background: #4CAF50;
            color: white;
            font-weight: bold;
            margin-top: 5px;
        }
        
        button:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        button.danger {
            background: #ff4444;
        }
        
        button.danger:hover {
            background: #cc0000;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        input[type="range"] {
            flex: 1;
            -webkit-appearance: none;
            height: 5px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            outline: none;
        }
        
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #ff6b6b;
            cursor: pointer;
        }
        
        .slider-value {
            min-width: 40px;
            text-align: right;
            color: #ff6b6b;
        }
        
        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
        }
        
        .blast-indicator {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(255, 0, 0, 0.6);
            border: 2px solid #ff0000;
            pointer-events: none;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.5); opacity: 0.5; }
            100% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <div id="loading">加载中...</div>
        <div id="controls">
            <h3>🏗️ 建筑爆破控制面板</h3>
            
            <div class="control-group">
                <label>爆破模式：</label>
                <select id="blastMode">
                    <option value="controlled">控制爆破（定向倒塌）</option>
                    <option value="sequential">顺序爆破（从下到上）</option>
                    <option value="simultaneous">同步爆破（多点同时）</option>
                    <option value="implosion">内爆（向内坍塌）</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>爆破延迟倍率：</label>
                <div class="slider-container">
                    <input type="range" id="delayMultiplier" min="0.1" max="3" step="0.1" value="1">
                    <span class="slider-value">1.0x</span>
                </div>
            </div>
            
            <div class="control-group">
                <button id="previewBtn">预览爆破点</button>
                <button id="resetBtn">重置建筑</button>
                <button id="detonateBtn" class="danger">💥 执行爆破</button>
            </div>
            
            <div class="control-group">
                <label>相机视角：</label>
                <button id="cameraView1">正面视角</button>
                <button id="cameraView2">俯视视角</button>
                <button id="cameraView3">环绕视角</button>
            </div>
        </div>
        
        <div id="info">
            鼠标左键：旋转视角 | 鼠标右键：平移 | 滚轮：缩放
        </div>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.169.0/build/three.webgpu.js",
            "three/tsl": "https://unpkg.com/three@0.169.0/build/three.webgpu.js",
            "three/addons/": "https://unpkg.com/three@0.169.0/examples/jsm/",
            "cannon-es": "https://unpkg.com/cannon-es@0.20.0/dist/cannon-es.js"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import WebGPU from 'three/addons/capabilities/WebGPU.js';
        import * as CANNON from 'cannon-es';

        // WebGPU Node Materials 和 TSL 函数
        const MeshStandardNodeMaterial = THREE.MeshStandardNodeMaterial || THREE.MeshStandardMaterial;
        const MeshBasicNodeMaterial = THREE.MeshBasicNodeMaterial || THREE.MeshBasicMaterial;
        const PointsNodeMaterial = THREE.PointsNodeMaterial || THREE.PointsMaterial;

        // TSL 函数 - 从 THREE 命名空间获取或提供回退
        const vec3 = THREE.vec3 || (() => new THREE.Vector3());
        const vec4 = THREE.vec4 || (() => new THREE.Vector4());
        const float = THREE.float || ((v) => v);
        const uniform = THREE.uniform || ((v) => ({ value: v }));
        const instancedBufferAttribute = THREE.instancedBufferAttribute || (() => null);
        const normalWorld = THREE.normalWorld || new THREE.Vector3(0, 1, 0);
        const positionWorld = THREE.positionWorld || new THREE.Vector3();
        const uv = THREE.uv || (() => new THREE.Vector2());
        const length = THREE.length || ((v) => v.length());
        const smoothstep = THREE.smoothstep || ((edge0, edge1, x) => {
            const t = Math.max(0, Math.min(1, (x - edge0) / (edge1 - edge0)));
            return t * t * (3 - 2 * t);
        });

        // 全局变量
        let scene, camera, renderer, controls;
        let world;
        let building = [];
        let blastPoints = [];
        let particleSystems = [];
        let isExploding = false;
        let animationId;
        
        // 建筑参数
        const BUILDING_PARAMS = {
            radius: 10,
            bricksPerLayer: 20,
            layers: 60,
            brickWidth: 2,
            brickHeight: 1,
            brickDepth: 1
        };

        // 爆破模式
        const BlastingModes = {
            controlled: '控制爆破',
            sequential: '顺序爆破',
            simultaneous: '同步爆破',
            implosion: '内爆'
        };

        // 初始化Three.js场景
        async function initThree() {
            // 检测 WebGPU 支持
            if (WebGPU.isAvailable() === false) {
                console.warn('WebGPU 不可用，将使用 WebGL 渲染器');
                document.getElementById('loading').innerHTML = '正在加载... (使用 WebGL)';
            }

            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB); // 天空蓝
            scene.fog = new THREE.Fog(0x87CEEB, 50, 200);

            // 相机
            camera = new THREE.PerspectiveCamera(
                75,
                window.innerWidth / window.innerHeight,
                0.1,
                1000
            );
            camera.position.set(40, 30, 40);
            camera.lookAt(0, 20, 0);

            // WebGPU 渲染器
            renderer = new THREE.WebGPURenderer({
                antialias: true,
                powerPreference: 'high-performance',
                forceWebGL: !WebGPU.isAvailable()
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(window.devicePixelRatio);

            // 性能优化设置
            renderer.info.autoReset = false;
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            document.getElementById('canvas-container').appendChild(renderer.domElement);

            try {
                // WebGPU 初始化
                await renderer.init();
                console.log('WebGPU 渲染器初始化成功');

                // 控制器
                controls = new OrbitControls(camera, renderer.domElement);
                controls.enableDamping = true;
                controls.dampingFactor = 0.05;
                controls.target.set(0, 20, 0);
                controls.update();

                // 灯光
                setupLighting();

                // 地面
                createGround();

                // 初始化物理世界
                initPhysics();

                // 创建建筑
                await createBuilding();

                // 预编译场景
                await renderer.compileAsync(scene, camera);
                console.log('场景预编译完成');

            } catch (error) {
                console.error('初始化失败:', error);
            }

            // 移除加载提示
            document.getElementById('loading').style.display = 'none';
        }

        // 设置灯光
        function setupLighting() {
            // 环境光
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            // 主方向光（太阳光）
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(50, 100, 50);
            directionalLight.castShadow = true;
            directionalLight.shadow.camera.left = -50;
            directionalLight.shadow.camera.right = 50;
            directionalLight.shadow.camera.top = 50;
            directionalLight.shadow.camera.bottom = -50;
            directionalLight.shadow.camera.near = 0.1;
            directionalLight.shadow.camera.far = 200;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // 半球光（天空光）
            const hemisphereLight = new THREE.HemisphereLight(0x87CEEB, 0x545454, 0.4);
            scene.add(hemisphereLight);
        }

        // 创建地面
        function createGround() {
            const groundGeometry = new THREE.PlaneGeometry(80, 80);
            
            // 使用标准材质（WebGPU 会自动优化）
            const groundMaterial = new THREE.MeshStandardMaterial({
                color: 0x404040,
                roughness: 0.8,
                metalness: 0.2
            });
            
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            ground.static = true; // 标记为静态物体
            scene.add(ground);
        }

        // 初始化物理世界
        function initPhysics() {
            world = new CANNON.World({
                gravity: new CANNON.Vec3(0, -9.82, 0)
            });
            world.broadphase = new CANNON.SAPBroadphase(world);
            world.solver.iterations = 10;
            world.defaultContactMaterial.friction = 0.8;
            world.defaultContactMaterial.restitution = 0.05;

            // 创建地面物理体
            const groundShape = new CANNON.Box(new CANNON.Vec3(40, 0.1, 40));
            const groundBody = new CANNON.Body({
                mass: 0,
                shape: groundShape,
                position: new CANNON.Vec3(0, -0.1, 0)
            });
            world.addBody(groundBody);
        }

        // 创建砖块
        function createBrick(x, y, z, rotation) {
            // Three.js网格
            const geometry = new THREE.BoxGeometry(
                BUILDING_PARAMS.brickWidth,
                BUILDING_PARAMS.brickHeight,
                BUILDING_PARAMS.brickDepth
            );
            
            // 使用标准材质（WebGPU 会自动优化）
            const brickMaterial = new THREE.MeshStandardMaterial({
                color: 0xD2B48C, // 米色
                roughness: 0.7,
                metalness: 0.1
            });
            
            const mesh = new THREE.Mesh(geometry, brickMaterial);
            mesh.position.set(x, y, z);
            mesh.rotation.y = rotation;
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            mesh.static = true; // 初始标记为静态
            scene.add(mesh);

            // Cannon.js物理体
            const shape = new CANNON.Box(new CANNON.Vec3(
                BUILDING_PARAMS.brickWidth / 2,
                BUILDING_PARAMS.brickHeight / 2,
                BUILDING_PARAMS.brickDepth / 2
            ));
            const body = new CANNON.Body({
                mass: 5,
                shape: shape,
                position: new CANNON.Vec3(x, y, z),
                sleepSpeedLimit: 0.01,
                sleepTimeLimit: 1
            });
            
            // 设置初始旋转
            const quaternion = new CANNON.Quaternion();
            quaternion.setFromAxisAngle(new CANNON.Vec3(0, 1, 0), rotation);
            body.quaternion = quaternion;
            
            // 初始睡眠状态
            body.allowSleep = true;
            body.sleep();
            
            world.addBody(body);

            return { mesh, body };
        }

        // 创建圆形建筑
        async function createBuilding() {
            building = [];
            
            for (let layer = 0; layer < BUILDING_PARAMS.layers; layer++) {
                const angleStep = (Math.PI * 2) / BUILDING_PARAMS.bricksPerLayer;
                
                for (let i = 0; i < BUILDING_PARAMS.bricksPerLayer; i++) {
                    let angle = i * angleStep;
                    
                    // 偶数层偏移50%
                    if (layer % 2 === 0) {
                        angle += angleStep * 0.5;
                    }
                    
                    // 微调半径确保紧密贴合
                    const adjustedRadius = BUILDING_PARAMS.radius - BUILDING_PARAMS.brickDepth * 0.45;
                    
                    const x = Math.cos(angle) * adjustedRadius;
                    const y = layer * BUILDING_PARAMS.brickHeight + BUILDING_PARAMS.brickHeight / 2;
                    const z = Math.sin(angle) * adjustedRadius;
                    const rotation = angle + Math.PI / 2;
                    
                    const brick = createBrick(x, y, z, rotation);
                    building.push(brick);
                }
            }
            
            // 让建筑稳定几帧
            return new Promise(resolve => {
                setTimeout(() => {
                    building.forEach(brick => {
                        brick.body.sleep();
                    });
                    resolve();
                }, 100);
            });
        }

        // 配置爆破点
        function configureBlastPoints(mode) {
            blastPoints = [];
            
            switch(mode) {
                case 'controlled':
                    setupControlledDemolition();
                    break;
                case 'sequential':
                    setupSequentialBlasting();
                    break;
                case 'simultaneous':
                    setupSimultaneousBlasting();
                    break;
                case 'implosion':
                    setupImplosion();
                    break;
            }
        }

        // 控制爆破配置
        function setupControlledDemolition() {
            // 底层关键支撑点（东侧先爆）
            for (let i = 0; i < 4; i++) {
                const angle = (Math.PI * 2 / 4) * i;
                blastPoints.push({
                    position: new THREE.Vector3(
                        Math.cos(angle) * BUILDING_PARAMS.radius,
                        1,
                        Math.sin(angle) * BUILDING_PARAMS.radius
                    ),
                    delay: i < 2 ? 0 : 100,
                    force: 800,
                    radius: 3,
                    type: 'primary'
                });
            }
            
            // 中层削弱点
            for (let layer = 10; layer < 40; layer += 10) {
                const angle = (layer / 10) * Math.PI / 2;
                blastPoints.push({
                    position: new THREE.Vector3(
                        Math.cos(angle) * BUILDING_PARAMS.radius,
                        layer,
                        Math.sin(angle) * BUILDING_PARAMS.radius
                    ),
                    delay: 200 + layer * 5,
                    force: 500,
                    radius: 2,
                    type: 'secondary'
                });
            }
            
            // 顶层引导点
            blastPoints.push({
                position: new THREE.Vector3(
                    -BUILDING_PARAMS.radius,
                    50,
                    0
                ),
                delay: 400,
                force: 300,
                radius: 2,
                type: 'guide'
            });
        }

        // 顺序爆破配置
        function setupSequentialBlasting() {
            const layersToBlast = [0, 10, 20, 30, 40, 50];
            
            layersToBlast.forEach((layer, index) => {
                for (let i = 0; i < 4; i++) {
                    const angle = (Math.PI * 2 / 4) * i;
                    blastPoints.push({
                        position: new THREE.Vector3(
                            Math.cos(angle) * BUILDING_PARAMS.radius,
                            layer + 1,
                            Math.sin(angle) * BUILDING_PARAMS.radius
                        ),
                        delay: index * 150,
                        force: 600,
                        radius: 2.5,
                        type: 'sequential'
                    });
                }
            });
        }

        // 同步爆破配置
        function setupSimultaneousBlasting() {
            // 在建筑的多个关键点同时爆破
            const positions = [
                { layer: 0, count: 8 },
                { layer: 20, count: 6 },
                { layer: 40, count: 4 }
            ];
            
            positions.forEach(pos => {
                for (let i = 0; i < pos.count; i++) {
                    const angle = (Math.PI * 2 / pos.count) * i;
                    blastPoints.push({
                        position: new THREE.Vector3(
                            Math.cos(angle) * BUILDING_PARAMS.radius,
                            pos.layer + 1,
                            Math.sin(angle) * BUILDING_PARAMS.radius
                        ),
                        delay: 0, // 同时爆破
                        force: 700,
                        radius: 3,
                        type: 'simultaneous'
                    });
                }
            });
        }

        // 内爆配置
        function setupImplosion() {
            const centerLayers = [5, 15, 25, 35];
            
            centerLayers.forEach((layer, index) => {
                for (let i = 0; i < 8; i++) {
                    const angle = (Math.PI * 2 / 8) * i;
                    const radius = BUILDING_PARAMS.radius * 0.5;
                    
                    blastPoints.push({
                        position: new THREE.Vector3(
                            Math.cos(angle) * radius,
                            layer,
                            Math.sin(angle) * radius
                        ),
                        delay: index * 50,
                        force: 600,
                        radius: 2.5,
                        type: 'implosion',
                        direction: 'inward'
                    });
                }
            });
        }

        // 预览爆破点
        function previewBlastPoints() {
            // 清除之前的预览
            document.querySelectorAll('.blast-indicator').forEach(el => el.remove());
            
            blastPoints.forEach((blast, index) => {
                // 创建爆破点标记
                const marker = new THREE.Mesh(
                    new THREE.SphereGeometry(0.5, 16, 16),
                    new THREE.MeshBasicMaterial({ 
                        color: blast.type === 'primary' ? 0xff0000 : 0xff6600,
                        transparent: true,
                        opacity: 0.6
                    })
                );
                marker.position.copy(blast.position);
                scene.add(marker);
                
                // 3秒后移除
                setTimeout(() => {
                    scene.remove(marker);
                }, 3000);
            });
        }

        // 执行爆破
        async function executeBlasting() {
            if (isExploding) return;
            isExploding = true;
            
            const delayMultiplier = parseFloat(document.getElementById('delayMultiplier').value);
            
            // 按延迟时间排序
            const sortedBlasts = [...blastPoints].sort((a, b) => a.delay - b.delay);
            
            for (const blast of sortedBlasts) {
                await delay(blast.delay * delayMultiplier);
                detonatePoint(blast);
                createBlastEffects(blast);
            }
            
            // 建筑开始倒塌后创建大规模尘埃云
            setTimeout(() => {
                createCollapseCloud();
            }, 1000);
        }

        // 延迟函数
        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 单点爆破
        function detonatePoint(blast) {
            building.forEach(brick => {
                const distance = brick.body.position.distanceTo(
                    new CANNON.Vec3(blast.position.x, blast.position.y, blast.position.z)
                );
                
                if (distance < blast.radius) {
                    brick.body.wakeUp();
                    brick.mesh.static = false; // 标记为动态物体
                    
                    let force;
                    if (blast.direction === 'inward') {
                        // 内爆
                        const toCenter = new CANNON.Vec3(
                            -blast.position.x,
                            0,
                            -blast.position.z
                        );
                        toCenter.normalize();
                        force = toCenter.scale(blast.force);
                    } else {
                        // 外爆
                        const direction = brick.body.position.vsub(
                            new CANNON.Vec3(blast.position.x, blast.position.y, blast.position.z)
                        );
                        direction.normalize();
                        const forceMagnitude = blast.force * (1 - distance / blast.radius);
                        force = direction.scale(forceMagnitude);
                    }
                    
                    brick.body.applyImpulse(force, brick.body.position);
                    
                    // 添加随机旋转
                    const torque = new CANNON.Vec3(
                        (Math.random() - 0.5) * blast.force * 0.1,
                        (Math.random() - 0.5) * blast.force * 0.1,
                        (Math.random() - 0.5) * blast.force * 0.1
                    );
                    brick.body.applyTorque(torque);
                }
            });
        }

        // 创建爆炸视觉效果
        function createBlastEffects(blast) {
            // 火花粒子系统
            createSparks(blast.position, blast.type);
            
            // 烟雾效果
            createSmoke(blast.position);
            
            // 冲击波
            createShockwave(blast.position, blast.radius);
            
            // 爆炸光源
            createExplosionLight(blast.position);
            
            // 混凝土碎片
            createDebris(blast.position, blast.type);
        }

        // 创建火花
        function createSparks(position, type) {
            const sparkCount = type === 'primary' ? 300 : 150;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(sparkCount * 3);
            const velocities = new Float32Array(sparkCount * 3);
            const colors = new Float32Array(sparkCount * 3);
            const lifeTimes = new Float32Array(sparkCount);
            
            for (let i = 0; i < sparkCount; i++) {
                const i3 = i * 3;
                
                // 位置
                positions[i3] = position.x;
                positions[i3 + 1] = position.y;
                positions[i3 + 2] = position.z;
                
                // 速度（随机方向）
                const speed = 10 + Math.random() * 20;
                const theta = Math.random() * Math.PI * 2;
                const phi = Math.random() * Math.PI;
                
                velocities[i3] = Math.sin(phi) * Math.cos(theta) * speed;
                velocities[i3 + 1] = Math.cos(phi) * speed;
                velocities[i3 + 2] = Math.sin(phi) * Math.sin(theta) * speed;
                
                // 颜色（黄色到橙色）
                colors[i3] = 1;
                colors[i3 + 1] = 0.5 + Math.random() * 0.5;
                colors[i3 + 2] = 0;
                
                // 生命周期
                lifeTimes[i] = 0;
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('velocity', new THREE.InstancedBufferAttribute(velocities, 3));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
            geometry.setAttribute('life', new THREE.InstancedBufferAttribute(lifeTimes, 1));
            
            // 使用 WebGPU PointsNodeMaterial
            const sparkMaterial = new THREE.PointsNodeMaterial({
                transparent: true,
                blending: THREE.AdditiveBlending,
                depthWrite: false,
                vertexColors: true
            });

            // 粒子生命周期
            const particleLife = instancedBufferAttribute('life', 'float');

            // 粒子大小随生命周期变化
            sparkMaterial.sizeNode = float(0.3).mul(
                float(1).sub(particleLife.div(2))
            );

            // 粒子透明度衰减
            sparkMaterial.opacityNode = float(1).sub(particleLife.div(2));
            
            const sparks = new THREE.Points(geometry, sparkMaterial);
            scene.add(sparks);
            
            // 动画火花
            const startTime = Date.now();
            const animateSparks = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 2) {
                    scene.remove(sparks);
                    return;
                }
                
                const positions = sparks.geometry.attributes.position.array;
                const velocities = sparks.geometry.attributes.velocity.array;
                const lifeTimes = sparks.geometry.attributes.life.array;
                
                for (let i = 0; i < sparkCount; i++) {
                    const i3 = i * 3;
                    
                    // 更新位置
                    positions[i3] += velocities[i3] * 0.016;
                    positions[i3 + 1] += velocities[i3 + 1] * 0.016 - 9.8 * 0.016 * elapsed;
                    positions[i3 + 2] += velocities[i3 + 2] * 0.016;
                    
                    // 更新生命周期
                    lifeTimes[i] = elapsed;
                }
                
                sparks.geometry.attributes.position.needsUpdate = true;
                sparks.geometry.attributes.life.needsUpdate = true;
                
                requestAnimationFrame(animateSparks);
            };
            
            animateSparks();
        }

        // 创建烟雾
        function createSmoke(position) {
            const smokeCount = 50;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(smokeCount * 3);
            const sizes = new Float32Array(smokeCount);
            const randoms = new Float32Array(smokeCount);
            
            for (let i = 0; i < smokeCount; i++) {
                const i3 = i * 3;
                
                positions[i3] = position.x + (Math.random() - 0.5) * 2;
                positions[i3 + 1] = position.y;
                positions[i3 + 2] = position.z + (Math.random() - 0.5) * 2;
                
                sizes[i] = 2 + Math.random() * 3;
                randoms[i] = Math.random();
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('size', new THREE.InstancedBufferAttribute(sizes, 1));
            geometry.setAttribute('random', new THREE.InstancedBufferAttribute(randoms, 1));
            
            // 使用 WebGPU PointsNodeMaterial
            const smokeMaterial = new THREE.PointsNodeMaterial({
                transparent: true,
                depthWrite: false,
                color: 0x333333
            });

            // 烟雾时间
            const smokeTime = uniform(0);
            const smokeExpansion = smokeTime.mul(2).add(1);

            // 烟雾大小扩散
            smokeMaterial.sizeNode = instancedBufferAttribute('size', 'float').mul(smokeExpansion);

            // 烟雾透明度衰减
            smokeMaterial.opacityNode = float(0.6).div(smokeExpansion);

            // 存储时间uniform以便动画更新
            smokeMaterial.userData = { timeUniform: smokeTime };
            
            const smoke = new THREE.Points(geometry, smokeMaterial);
            scene.add(smoke);
            
            // 动画烟雾
            const startTime = Date.now();
            const animateSmoke = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 10) {
                    scene.remove(smoke);
                    return;
                }
                
                const positions = smoke.geometry.attributes.position.array;
                const randoms = smoke.geometry.attributes.random.array;
                
                for (let i = 0; i < smokeCount; i++) {
                    const i3 = i * 3;
                    
                    // 烟雾上升和扩散（使用随机值产生不同的运动）
                    positions[i3] += (Math.sin(elapsed * 3 + randoms[i] * 6.28) * 0.05);
                    positions[i3 + 1] += 0.05 + randoms[i] * 0.05;
                    positions[i3 + 2] += (Math.cos(elapsed * 3 + randoms[i] * 6.28) * 0.05);
                }
                
                smoke.geometry.attributes.position.needsUpdate = true;

                // 更新时间uniform
                smoke.material.userData.timeUniform.value = elapsed;
                
                requestAnimationFrame(animateSmoke);
            };
            
            animateSmoke();
        }

        // 创建冲击波
        function createShockwave(position, radius) {
            const geometry = new THREE.RingGeometry(0.1, radius * 2, 32);

            // 使用 WebGPU Node Material
            const shockwaveMaterial = new THREE.MeshBasicNodeMaterial({
                transparent: true,
                side: THREE.DoubleSide
            });

            // 冲击波时间uniform
            const shockwaveTime = uniform(0);
            const waveRadius = shockwaveTime.mul(10);
            const thickness = float(0.2);

            // 从中心向外的渐变效果
            const uvDist = length(uv().sub(0.5).mul(2));
            const ring = smoothstep(waveRadius.sub(thickness), waveRadius, uvDist)
                .mul(smoothstep(waveRadius.add(thickness), waveRadius, uvDist));

            shockwaveMaterial.colorNode = vec4(1, 1, 1, ring.mul(0.5));

            // 存储时间uniform以便动画更新
            shockwaveMaterial.userData = { timeUniform: shockwaveTime };

            const shockwave = new THREE.Mesh(geometry, shockwaveMaterial);
            shockwave.position.copy(position);
            shockwave.rotation.x = -Math.PI / 2;
            scene.add(shockwave);

            // 动画冲击波
            const startTime = Date.now();
            const animateShockwave = () => {
                const elapsed = (Date.now() - startTime) / 1000;

                if (elapsed > 1) {
                    scene.remove(shockwave);
                    return;
                }

                const scale = 1 + elapsed * 10;
                shockwave.scale.set(scale, scale, scale);

                // 更新时间uniform
                shockwave.material.userData.timeUniform.value = elapsed;

                requestAnimationFrame(animateShockwave);
            };

            animateShockwave();
        }

        // 创建爆炸光源
        function createExplosionLight(position) {
            const light = new THREE.PointLight(0xffaa00, 10, 20);
            light.position.copy(position);
            scene.add(light);
            
            // 光源衰减动画
            const startTime = Date.now();
            const animateLight = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 0.5) {
                    scene.remove(light);
                    return;
                }
                
                light.intensity = 10 * (1 - elapsed * 2);
                
                requestAnimationFrame(animateLight);
            };
            
            animateLight();
        }

        // 创建混凝土碎片
        function createDebris(position, type) {
            const debrisCount = type === 'primary' ? 30 : 15;
            const debrisGroup = new THREE.Group();
            
            for (let i = 0; i < debrisCount; i++) {
                // 创建不规则碎片几何体
                const size = 0.1 + Math.random() * 0.3;
                const geometry = new THREE.TetrahedronGeometry(size, 0);
                
                // 随机变形
                const positions = geometry.attributes.position.array;
                for (let j = 0; j < positions.length; j += 3) {
                    positions[j] += (Math.random() - 0.5) * size * 0.3;
                    positions[j + 1] += (Math.random() - 0.5) * size * 0.3;
                    positions[j + 2] += (Math.random() - 0.5) * size * 0.3;
                }
                geometry.attributes.position.needsUpdate = true;
                
                const material = new THREE.MeshStandardMaterial({
                    color: 0x8B7355, // 深褐色
                    roughness: 0.9,
                    metalness: 0
                });
                
                const debris = new THREE.Mesh(geometry, material);
                debris.position.copy(position);
                debris.position.add(new THREE.Vector3(
                    (Math.random() - 0.5) * 2,
                    Math.random(),
                    (Math.random() - 0.5) * 2
                ));
                
                // 初始速度
                debris.userData.velocity = new THREE.Vector3(
                    (Math.random() - 0.5) * 10,
                    Math.random() * 15,
                    (Math.random() - 0.5) * 10
                );
                
                // 初始旋转速度
                debris.userData.angularVelocity = new THREE.Vector3(
                    Math.random() * 10,
                    Math.random() * 10,
                    Math.random() * 10
                );
                
                debris.castShadow = true;
                debrisGroup.add(debris);
            }
            
            scene.add(debrisGroup);
            
            // 动画碎片
            const startTime = Date.now();
            const animateDebris = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 5) {
                    scene.remove(debrisGroup);
                    return;
                }
                
                debrisGroup.children.forEach(debris => {
                    // 更新位置
                    debris.position.add(debris.userData.velocity.clone().multiplyScalar(0.016));
                    
                    // 重力
                    debris.userData.velocity.y -= 9.8 * 0.016;
                    
                    // 旋转
                    debris.rotation.x += debris.userData.angularVelocity.x * 0.016;
                    debris.rotation.y += debris.userData.angularVelocity.y * 0.016;
                    debris.rotation.z += debris.userData.angularVelocity.z * 0.016;
                    
                    // 地面碰撞
                    if (debris.position.y < 0.1) {
                        debris.position.y = 0.1;
                        debris.userData.velocity.y *= -0.3; // 弹性碰撞
                        debris.userData.velocity.x *= 0.8; // 摩擦
                        debris.userData.velocity.z *= 0.8;
                        
                        // 碰撞时产生小尘土
                        if (Math.abs(debris.userData.velocity.y) > 1) {
                            createDustPuff(debris.position);
                        }
                    }
                });
                
                requestAnimationFrame(animateDebris);
            };
            
            animateDebris();
        }

        // 创建尘土飞扬效果
        function createDustPuff(position) {
            const dustCount = 10;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(dustCount * 3);
            const sizes = new Float32Array(dustCount);
            
            for (let i = 0; i < dustCount; i++) {
                const i3 = i * 3;
                
                positions[i3] = position.x + (Math.random() - 0.5) * 0.5;
                positions[i3 + 1] = 0.1;
                positions[i3 + 2] = position.z + (Math.random() - 0.5) * 0.5;
                
                sizes[i] = 0.5 + Math.random() * 0.5;
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
            
            const material = new THREE.PointsMaterial({
                size: 1,
                color: 0x8B7D6B, // 尘土色
                transparent: true,
                opacity: 0.4,
                depthWrite: false
            });
            
            const dust = new THREE.Points(geometry, material);
            scene.add(dust);
            
            // 动画尘土
            const startTime = Date.now();
            const animateDust = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 2) {
                    scene.remove(dust);
                    return;
                }
                
                const positions = dust.geometry.attributes.position.array;
                
                for (let i = 0; i < dustCount; i++) {
                    const i3 = i * 3;
                    
                    // 尘土上升和扩散
                    positions[i3] += (Math.random() - 0.5) * 0.02;
                    positions[i3 + 1] += 0.01;
                    positions[i3 + 2] += (Math.random() - 0.5) * 0.02;
                }
                
                dust.geometry.attributes.position.needsUpdate = true;
                dust.material.opacity = 0.4 * (1 - elapsed / 2);
                dust.material.size = 1 + elapsed;
                
                requestAnimationFrame(animateDust);
            };
            
            animateDust();
        }

        // 创建大规模倒塌尘埃云
        function createCollapseCloud() {
            const cloudParticles = 500;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(cloudParticles * 3);
            const sizes = new Float32Array(cloudParticles);
            const velocities = new Float32Array(cloudParticles * 3);
            const randoms = new Float32Array(cloudParticles);
            
            for (let i = 0; i < cloudParticles; i++) {
                const i3 = i * 3;
                
                // 从建筑底部开始
                const angle = Math.random() * Math.PI * 2;
                const radius = BUILDING_PARAMS.radius + Math.random() * 5;
                
                positions[i3] = Math.cos(angle) * radius;
                positions[i3 + 1] = Math.random() * 5;
                positions[i3 + 2] = Math.sin(angle) * radius;
                
                // 向外扩散的速度
                velocities[i3] = Math.cos(angle) * (2 + Math.random() * 3);
                velocities[i3 + 1] = Math.random() * 2;
                velocities[i3 + 2] = Math.sin(angle) * (2 + Math.random() * 3);
                
                sizes[i] = 5 + Math.random() * 10;
                randoms[i] = Math.random();
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('size', new THREE.InstancedBufferAttribute(sizes, 1));
            geometry.setAttribute('velocity', new THREE.InstancedBufferAttribute(velocities, 3));
            geometry.setAttribute('random', new THREE.InstancedBufferAttribute(randoms, 1));
            
            // 使用 WebGPU PointsNodeMaterial
            const cloudMaterial = new THREE.PointsNodeMaterial({
                transparent: true,
                depthWrite: false,
                blending: THREE.NormalBlending,
                color: 0x666666
            });
            
            // 时间uniform
            const cloudTime = uniform(0);
            const cloudExpansion = cloudTime.mul(0.5).add(1);
            
            // 粒子大小随时间增长
            cloudMaterial.sizeNode = instancedBufferAttribute('size', 'float').mul(cloudExpansion);
            
            // 透明度随时间衰减
            cloudMaterial.opacityNode = float(0.3).div(cloudExpansion.pow(0.5));
            
            // 存储时间uniform
            cloudMaterial.userData = { timeUniform: cloudTime };
            
            const cloud = new THREE.Points(geometry, cloudMaterial);
            scene.add(cloud);
            
            // 动画尘埃云
            const startTime = Date.now();
            const animateCloud = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                
                if (elapsed > 15) {
                    scene.remove(cloud);
                    return;
                }
                
                const positions = cloud.geometry.attributes.position.array;
                const velocities = cloud.geometry.attributes.velocity.array;
                
                for (let i = 0; i < cloudParticles; i++) {
                    const i3 = i * 3;
                    
                    // 更新位置
                    positions[i3] += velocities[i3] * 0.016;
                    positions[i3 + 1] += velocities[i3 + 1] * 0.016;
                    positions[i3 + 2] += velocities[i3 + 2] * 0.016;
                    
                    // 速度衰减
                    velocities[i3] *= 0.99;
                    velocities[i3 + 1] *= 0.99;
                    velocities[i3 + 2] *= 0.99;
                }
                
                cloud.geometry.attributes.position.needsUpdate = true;
                cloud.geometry.attributes.velocity.needsUpdate = true;
                
                // 更新时间uniform
                cloud.material.userData.timeUniform.value = elapsed;
                
                requestAnimationFrame(animateCloud);
            };
            
            animateCloud();
        }

        // 重置场景
        async function resetScene() {
            // 停止爆炸
            isExploding = false;
            
            // 清除旧建筑
            building.forEach(brick => {
                scene.remove(brick.mesh);
                world.removeBody(brick.body);
            });
            building = [];
            
            // 清除粒子系统
            particleSystems.forEach(system => {
                scene.remove(system);
            });
            particleSystems = [];
            
            // 重新创建建筑
            await createBuilding();
        }

        // 动画循环
        async function animate() {
            // 更新物理世界
            world.step(1/60);

            // 同步物理和渲染
            building.forEach(brick => {
                brick.mesh.position.copy(brick.body.position);
                brick.mesh.quaternion.copy(brick.body.quaternion);
            });

            // 更新控制器
            controls.update();

            // 异步渲染场景
            await renderer.renderAsync(scene, camera);
        }

        // 设置UI事件
        function setupUI() {
            // 爆破模式选择
            const blastModeSelect = document.getElementById('blastMode');
            blastModeSelect.addEventListener('change', (e) => {
                configureBlastPoints(e.target.value);
            });
            
            // 延迟倍率滑块
            const delaySlider = document.getElementById('delayMultiplier');
            const delayValue = document.querySelector('.slider-value');
            delaySlider.addEventListener('input', (e) => {
                delayValue.textContent = e.target.value + 'x';
            });
            
            // 预览爆破点按钮
            document.getElementById('previewBtn').addEventListener('click', () => {
                configureBlastPoints(blastModeSelect.value);
                previewBlastPoints();
            });
            
            // 重置按钮
            document.getElementById('resetBtn').addEventListener('click', async () => {
                await resetScene();
                document.getElementById('detonateBtn').disabled = false;
            });
            
            // 爆破按钮
            document.getElementById('detonateBtn').addEventListener('click', () => {
                if (!isExploding) {
                    configureBlastPoints(blastModeSelect.value);
                    executeBlasting();
                    document.getElementById('detonateBtn').disabled = true;
                }
            });
            
            // 相机视角按钮
            document.getElementById('cameraView1').addEventListener('click', () => {
                camera.position.set(40, 30, 40);
                controls.target.set(0, 20, 0);
                controls.update();
            });
            
            document.getElementById('cameraView2').addEventListener('click', () => {
                camera.position.set(0, 80, 0);
                controls.target.set(0, 20, 0);
                controls.update();
            });
            
            document.getElementById('cameraView3').addEventListener('click', () => {
                let angle = 0;
                const radius = 50;
                const rotateCamera = () => {
                    angle += 0.01;
                    camera.position.x = Math.cos(angle) * radius;
                    camera.position.z = Math.sin(angle) * radius;
                    camera.position.y = 30;
                    camera.lookAt(0, 20, 0);
                    
                    if (angle < Math.PI * 2) {
                        requestAnimationFrame(rotateCamera);
                    }
                };
                rotateCamera();
            });
        }

        // 窗口大小调整
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        // 初始化
        async function init() {
            await initThree();
            setupUI();

            // 设置默认爆破模式
            configureBlastPoints('controlled');

            // 设置异步动画循环
            renderer.setAnimationLoop(animate);

            // 监听窗口大小变化
            window.addEventListener('resize', onWindowResize);
        }

        // 启动应用
        init().catch(error => {
            console.error('应用启动失败:', error);
            document.getElementById('loading').innerHTML = '加载失败: ' + error.message;
        });
    </script>
</body>
</html>